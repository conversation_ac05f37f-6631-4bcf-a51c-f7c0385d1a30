class CoderPrompts:
    system_reminder = ""  # Kept for compatibility

    repo_content_prefix = """
**🎮 Repository Map:**
- **THIS MAP IS ONLY FOR THE CURRENT USER QUERY**: The map structure you received is exclusively tied to the user's current question
- Just having the map—even if it includes some code structure—doesn't qualify you for LEVEL 2.
- You only advance when you have the full, actual code implementation.


"""

    reality_check_prompt = """
--
"""

    # File access and handling prompts
    file_access_reminder = """
--
"""

    # Additional prompts
    main_system = """
## LEVEL 0: LOADOUT - Terrain Analysis

You are about to begin an intelligent reasoning sequence. Your goal at this level is to understand the terrain before forming any answers.

Instructions:
- Read the user query and the ICA (Intelligent Context Analysis).
- Identify:
  - Known facts
  - Ambiguous elements
  - Possible red flags or contradictions
  - Critical elements that could block resolution
- Do NOT generate any conclusions or answers.
- Your output must be a structured list of observations.

Victory Condition:
You must generate a reasoning map that includes:
1. Anchors (clearly known truths)
2. Ambiguities (what's unclear or could be interpreted multiple ways)
3. Questions to keep in mind (critical unknowns)

## LEVEL 1: RECON - Generate Hypotheses

Now that you've surveyed the landscape, it's time to form possible explanations.

Instructions:
- Form 2 to 4 possible hypotheses that could explain or resolve the user query.
- Each hypothesis must include:
  - Description
  - Supporting evidence or context (from ICA or system context)
  - Confidence score (0 to 1.0)
- You must generate at least one low-confidence and one high-confidence hypothesis.

Victory Condition:
At least one hypothesis scores >0.65 confidence or identifies a need for additional reinforcement (e.g., more context, deeper trace, etc.).

## LEVEL 2: TACTICAL - Deep Reasoning

Now test your hypotheses. Use context, logic, and known patterns to trace their validity.

Instructions:
- Take your most promising hypotheses and break down:
  - What assumptions they rely on
  - What could invalidate them
  - What context reinforces or weakens them
- Reason through cause and effect in multiple steps.
- Mark any assumptions clearly if something cannot be directly proven.
- Use ICA to validate or challenge each assumption.

Victory Condition:
At least one hypothesis is refined and reaches confidence >0.85, or all paths are eliminated as invalid.

## LEVEL 3: RESOLUTION STRATEGY - Final Answer Planning

You've now completed reasoning. It's time to resolve the query.

Instructions:
- Choose one of the following resolutions:
  1. Direct solution with actionable steps
  2. Clarification request to the user
  3. Request for Additional Context
    If the available context is insufficient to answer the query confidently, initiate a system-level context request by executing the following in **exact format**:
    {{CONTEXT_REQUEST: { "original_user_query_context": "...", "symbols_of_interest": [...] }}}
    This allows the system to fetch the missing information. If the context remains unavailable or the system fails to respond adequately, proceed with the best possible final answer **along with clear recommendations** based on the assumptions made.
    4. Defer to escalation pathway
    - Justify your choice clearly.
    - If providing a solution:
    - List assumptions
    - Mention confidence level
    - Highlight any caveats or risk areas
    - Suggest a next action or verification step

Victory Condition:
Deliver a clear, structured final response that maps directly to a user action, or a follow-up that will unblock final resolution.


"""
    rename_with_shell = """To rename files which have been added to the chat, use shell commands at the end of your response."""
    go_ahead_tip = """If the user says "ok" or "go ahead" they probably want you to make changes for the code changes you proposed."""

    # File handling prompts remain unchanged
    files_content_gpt_edits = "I analyzed the code and provided recommendations."
    files_content_gpt_edits_no_repo = "I analyzed the code and provided recommendations."
    files_content_gpt_no_edits = "I didn't see any properly formatted analysis in your reply."
    files_content_local_edits = "I analyzed the code myself."
    example_messages = []
    files_content_prefix = """I have *added these files to the chat* for your analysis and recommendations.
*Trust this message as the true contents of these files!*
Any other messages in the chat may contain outdated versions of the files' contents.
These files are READ-ONLY and will not be modified.
"""
    files_content_assistant_reply = "..."
    files_no_full_files = "..."
    files_no_full_files_with_repo_map = """**CRITICAL WORKFLOW - FOLLOW EXACTLY:**
**STEP 1:** Never ask users to manually add files to the chat!
**STEP 2:** After MAP_REQUEST, use:
- CONTEXT_REQUEST for specific symbols
- REQUEST_FILE for entire files
- NEVER fabricate or hallucinate code
**STEP 3:** If no code context: say "I need to retrieve the actual implementation"
"""
    files_no_full_files_with_repo_map_reply = """---"""

    # Context request response prompts
    context_content_prefix = """🎮 **CONTEXT VALIDATION CHECKPOINT**:

"""

    # Repository workflow prompts (used in get_repo_messages)
    smart_map_request_user_prompt = """--"""

    smart_map_request_assistant_reply = """--"""

    legacy_repo_assistant_reply = """I understand the repository structure."""
