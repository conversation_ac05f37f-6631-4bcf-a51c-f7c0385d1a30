#!/usr/bin/env python3
"""
Test the simplified IR_REQUEST system with the new JSON format and response templates.
"""

import json
import sys
import os

# Add the aider-main directory to the path to import aider modules
aider_main_path = os.path.join(os.getcwd(), 'aider-main')
if aider_main_path not in sys.path:
    sys.path.insert(0, aider_main_path)

from aider.context_request.context_request_handler import ContextRequest<PERSON><PERSON><PERSON>, IRContextRequest


def test_simplified_ir_request():
    """Test the simplified IR_REQUEST system."""
    print("🧠 Testing Simplified IR_REQUEST System")
    print("=" * 60)
    
    # Initialize handler
    handler = ContextRequestHandler('.')
    
    # Test 1: Simplified debugging request
    print("\n🔧 Test 1: Debugging Request")
    print("-" * 40)
    
    # Simulate LLM sending simplified JSON
    llm_request = {
        "focus_entities": ["cache", "performance", "slow"],
        "task_type": "debugging"
    }
    
    print(f"📤 LLM Request: {json.dumps(llm_request, indent=2)}")
    
    # Create IRContextRequest with auto-generated fields
    ir_request = IRContextRequest(
        user_query="Why is my context selection taking so long?",  # Auto-filled
        task_description="Provide context for: Why is my context selection taking so long?",  # Auto-generated
        task_type=llm_request["task_type"],
        focus_entities=llm_request["focus_entities"],
        max_tokens=8000,
        include_ir_slices=True,
        include_code_context=True,
        llm_friendly=True,
        max_output_chars=30000,
        max_entities=10
    )
    
    # Process the request
    result = handler.process_ir_context_request(ir_request)
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
        return
    
    # Display results
    summary = result.get("summary", {})
    print(f"✅ Processing complete:")
    print(f"   - Selected Entities: {result.get('context_bundle', {}).get('total_entities', 0)}")
    print(f"   - Token Utilization: {summary.get('token_utilization', 'N/A')}")
    print(f"   - Critical Entities: {summary.get('critical_entities', 0)}")
    print(f"   - LLM Package Size: {len(result.get('llm_friendly_package', ''))} chars")
    
    # Save the LLM-friendly package
    if "llm_friendly_package" in result:
        with open("test_simplified_debug_package.txt", "w", encoding="utf-8") as f:
            f.write(result["llm_friendly_package"])
        print(f"💾 Saved LLM package: test_simplified_debug_package.txt")
    
    # Test 2: Feature development request
    print("\n\n🚀 Test 2: Feature Development Request")
    print("-" * 40)
    
    llm_request2 = {
        "focus_entities": ["extract", "method", "add", "new"],
        "task_type": "feature_development"
    }
    
    print(f"📤 LLM Request: {json.dumps(llm_request2, indent=2)}")
    
    ir_request2 = IRContextRequest(
        user_query="How do I add a new extraction method?",
        task_description="Provide context for: How do I add a new extraction method?",
        task_type=llm_request2["task_type"],
        focus_entities=llm_request2["focus_entities"],
        max_tokens=6000,
        include_ir_slices=True,
        include_code_context=True,
        llm_friendly=True,
        max_output_chars=25000,
        max_entities=8
    )
    
    result2 = handler.process_ir_context_request(ir_request2)
    
    if "error" in result2:
        print(f"❌ Error: {result2['error']}")
        return
    
    summary2 = result2.get("summary", {})
    print(f"✅ Processing complete:")
    print(f"   - Selected Entities: {result2.get('context_bundle', {}).get('total_entities', 0)}")
    print(f"   - Token Utilization: {summary2.get('token_utilization', 'N/A')}")
    print(f"   - Critical Entities: {summary2.get('critical_entities', 0)}")
    print(f"   - LLM Package Size: {len(result2.get('llm_friendly_package', ''))} chars")
    
    # Save the second package
    if "llm_friendly_package" in result2:
        with open("test_simplified_feature_package.txt", "w", encoding="utf-8") as f:
            f.write(result2["llm_friendly_package"])
        print(f"💾 Saved LLM package: test_simplified_feature_package.txt")
    
    # Test 3: Demonstrate response template
    print("\n\n📋 Test 3: Response Template Demonstration")
    print("-" * 40)
    
    # Simulate the new response template
    template_prefix = """📋 **INTELLIGENT CONTEXT PROVIDED**

Here's the relevant code context for your query, intelligently selected based on your requirements:

"""
    
    template_suffix = """

**Context Analysis Complete** ✅
- The above context was intelligently selected based on relevance scoring
- Full class implementations included (not just signatures)  
- Dependencies and relationships analyzed
- Ready for your analysis and implementation

**Next Steps:**
Please proceed with your analysis using this comprehensive context.

"""
    
    # Show how the template would be used
    if "llm_friendly_package" in result:
        full_response = template_prefix + result["llm_friendly_package"] + template_suffix
        print("📄 Sample Response Template:")
        print(full_response[:500] + "..." if len(full_response) > 500 else full_response)
    
    print("\n\n🎯 IR_REQUEST vs CONTEXT_REQUEST Comparison")
    print("=" * 60)
    
    print("✅ NEW IR_REQUEST Format:")
    print("   • Simplified JSON: only focus_entities and task_type")
    print("   • Auto-generated descriptions from user query")
    print("   • Intelligent context selection")
    print("   • LLM-optimized packages")
    print("   • Professional response templates")
    print("   • Single-step process")
    
    print("\n❌ OLD CONTEXT_REQUEST Format:")
    print("   • Complex JSON with many required fields")
    print("   • Manual symbol specification")
    print("   • Two-step MAP_REQUEST → CONTEXT_REQUEST process")
    print("   • Class signature extraction issues")
    print("   • Basic response format")
    
    print("\n🚀 Recommendation: Use IR_REQUEST for all new implementations!")


if __name__ == "__main__":
    test_simplified_ir_request()
